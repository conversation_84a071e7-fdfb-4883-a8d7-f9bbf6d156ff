import Foundation

// MARK: - 数据模型
struct MinimaxVoice: Codable {
    let voiceId: String
    let voiceName: String
    let description: String?
    
    enum CodingKeys: String, CodingKey {
        case voiceId = "voice_id"
        case voiceName = "voice_name"
        case description
    }
}

struct MinimaxTTSRequest: Codable {
    let text: String
    let model: String
    let voiceId: String
    
    enum CodingKeys: String, CodingKey {
        case text
        case model
        case voiceId = "voice_id"
    }
}

struct MinimaxVoiceCloneRequest: Codable {
    let tosFileName: String
    let voiceId: String
    let demoText: String
    let demoModel: String
    let needNoiseReduction: Bool
    
    enum CodingKeys: String, CodingKey {
        case tosFileName = "tos_file_name"
        case voiceId = "voice_id"
        case demoText = "demo_text"
        case demoModel = "demo_model"
        case needNoiseReduction = "need_noise_reduction"
    }
}

struct MinimaxResponse<T: Codable>: Codable {
    let code: Int?
    let message: String?
    let data: T?
}

// MARK: - Minimax语音服务类
class MinimaxVoiceService {
    
    // MARK: - 属性
    private let baseURL = "http://*************:5005/api/minimax"
    private let session = URLSession.shared
    
    // MARK: - 单例
    static let shared = MinimaxVoiceService()
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 获取所有系统音色
    /// - Parameter completion: 完成回调，返回音色列表或错误
    func getAllVoices(completion: @escaping (Result<[MinimaxVoice], Error>) -> Void) {
        let urlString = "\(baseURL)/voices"
        
        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        performRequest(request: request, completion: completion)
    }
    
    /// 生成语音（同步TTS）
    /// - Parameters:
    ///   - text: 要转换的文本
    ///   - model: 模型名称，默认为 "speech-02-hd"
    ///   - voiceId: 音色ID
    ///   - completion: 完成回调，返回音频数据或错误
    func generateSpeech(text: String, 
                       model: String = "speech-02-hd", 
                       voiceId: String, 
                       completion: @escaping (Result<Data, Error>) -> Void) {
        let urlString = "\(baseURL)/tts/sync/tos"
        
        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }
        
        let requestBody = MinimaxTTSRequest(text: text, model: model, voiceId: voiceId)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            completion(.failure(error))
            return
        }
        
        performDataRequest(request: request, completion: completion)
    }
    
    /// 克隆语音
    /// - Parameters:
    ///   - tosFileName: TOS文件名
    ///   - voiceId: 音色ID
    ///   - demoText: 试听文本，默认为 "这是您合成的音色，您可以试听一下"
    ///   - demoModel: 试听模型，默认为 "speech-02-hd"
    ///   - needNoiseReduction: 是否需要降噪，默认为 true
    ///   - completion: 完成回调，返回克隆结果或错误
    func cloneVoice(tosFileName: String,
                   voiceId: String,
                   demoText: String = "这是您合成的音色，您可以试听一下",
                   demoModel: String = "speech-02-hd",
                   needNoiseReduction: Bool = true,
                   completion: @escaping (Result<[String: Any], Error>) -> Void) {
        let urlString = "\(baseURL)/voice/clone/tos"
        
        guard let url = URL(string: urlString) else {
            completion(.failure(MinimaxError.invalidURL))
            return
        }
        
        let requestBody = MinimaxVoiceCloneRequest(
            tosFileName: tosFileName,
            voiceId: voiceId,
            demoText: demoText,
            demoModel: demoModel,
            needNoiseReduction: needNoiseReduction
        )
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
        } catch {
            completion(.failure(error))
            return
        }
        
        performDictionaryRequest(request: request, completion: completion)
    }
}

// MARK: - 私有方法
private extension MinimaxVoiceService {

    /// 执行网络请求并返回解码后的对象
    func performRequest<T: Codable>(request: URLRequest, completion: @escaping (Result<T, Error>) -> Void) {
        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                do {
                    let result = try JSONDecoder().decode(T.self, from: data)
                    completion(.success(result))
                } catch {
                    completion(.failure(error))
                }
            }
        }.resume()
    }

    /// 执行网络请求并返回原始数据
    func performDataRequest(request: URLRequest, completion: @escaping (Result<Data, Error>) -> Void) {
        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                completion(.success(data))
            }
        }.resume()
    }

    /// 执行网络请求并返回字典
    func performDictionaryRequest(request: URLRequest, completion: @escaping (Result<[String: Any], Error>) -> Void) {
        session.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    completion(.failure(MinimaxError.noData))
                    return
                }

                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        completion(.success(json))
                    } else {
                        completion(.failure(MinimaxError.invalidResponse))
                    }
                } catch {
                    completion(.failure(error))
                }
            }
        }.resume()
    }
}

// MARK: - 错误定义
enum MinimaxError: Error, LocalizedError {
    case invalidURL
    case noData
    case invalidResponse

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有返回数据"
        case .invalidResponse:
            return "无效的响应格式"
        }
    }
}
