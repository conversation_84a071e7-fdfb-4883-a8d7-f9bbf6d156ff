import Foundation
import AVFoundation

// MARK: - MinimaxVoiceService 使用示例
class MinimaxVoiceServiceExample {
    
    private let voiceService = MinimaxVoiceService.shared
    private var audioPlayer: AVAudioPlayer?
    
    // MARK: - 示例1: 获取所有音色
    func exampleGetAllVoices() {
        voiceService.getAllVoices { result in
            switch result {
            case .success(let voices):
                print("获取到 \(voices.count) 个音色:")
                for voice in voices {
                    print("- 音色ID: \(voice.voiceId), 名称: \(voice.voiceName)")
                    if let description = voice.description {
                        print("  描述: \(description)")
                    }
                }
            case .failure(let error):
                print("获取音色失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例2: 生成语音并播放
    func exampleGenerateSpeech() {
        let text = "今天天气也很不错啊,完成个人用户认证或企业用户认证，以确保可以正常使用本功能。"
        let voiceId = "a_1234567890"
        
        voiceService.generateSpeech(text: text, voiceId: voiceId) { [weak self] result in
            switch result {
            case .success(let audioData):
                print("语音生成成功，数据大小: \(audioData.count) bytes")
                self?.playAudio(data: audioData)
            case .failure(let error):
                print("语音生成失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例3: 克隆语音
    func exampleCloneVoice() {
        let tosFileName = "tts_sync_d1f57180cd83.mp3"
        let voiceId = "a_1234567890"
        
        voiceService.cloneVoice(tosFileName: tosFileName, voiceId: voiceId) { result in
            switch result {
            case .success(let response):
                print("语音克隆成功:")
                for (key, value) in response {
                    print("- \(key): \(value)")
                }
            case .failure(let error):
                print("语音克隆失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例4: 自定义参数的语音生成
    func exampleGenerateSpeechWithCustomParams() {
        let text = "这是一个自定义参数的语音合成示例"
        let model = "speech-02-hd"
        let voiceId = "custom_voice_id"
        
        voiceService.generateSpeech(text: text, model: model, voiceId: voiceId) { [weak self] result in
            switch result {
            case .success(let audioData):
                print("自定义语音生成成功")
                // 保存音频文件到本地
                self?.saveAudioToFile(data: audioData, fileName: "custom_speech.mp3")
            case .failure(let error):
                print("自定义语音生成失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 示例5: 自定义参数的语音克隆
    func exampleCloneVoiceWithCustomParams() {
        let tosFileName = "my_voice_sample.mp3"
        let voiceId = "my_custom_voice"
        let demoText = "这是我的专属音色，听起来怎么样？"
        let demoModel = "speech-02-hd"
        let needNoiseReduction = false
        
        voiceService.cloneVoice(
            tosFileName: tosFileName,
            voiceId: voiceId,
            demoText: demoText,
            demoModel: demoModel,
            needNoiseReduction: needNoiseReduction
        ) { result in
            switch result {
            case .success(let response):
                print("自定义语音克隆成功:")
                if let demoUrl = response["demo_url"] as? String {
                    print("试听地址: \(demoUrl)")
                }
                if let cloneId = response["clone_id"] as? String {
                    print("克隆ID: \(cloneId)")
                }
            case .failure(let error):
                print("自定义语音克隆失败: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 辅助方法
    
    /// 播放音频数据
    private func playAudio(data: Data) {
        do {
            audioPlayer = try AVAudioPlayer(data: data)
            audioPlayer?.play()
            print("开始播放音频")
        } catch {
            print("播放音频失败: \(error.localizedDescription)")
        }
    }
    
    /// 保存音频数据到文件
    private func saveAudioToFile(data: Data, fileName: String) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try data.write(to: audioURL)
            print("音频文件已保存到: \(audioURL.path)")
        } catch {
            print("保存音频文件失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 综合示例：完整的语音处理流程
    func exampleCompleteWorkflow() {
        print("开始完整的语音处理流程...")
        
        // 1. 首先获取可用的音色
        voiceService.getAllVoices { [weak self] result in
            switch result {
            case .success(let voices):
                guard let firstVoice = voices.first else {
                    print("没有可用的音色")
                    return
                }
                
                print("使用音色: \(firstVoice.voiceName) (ID: \(firstVoice.voiceId))")
                
                // 2. 使用第一个音色生成语音
                let text = "欢迎使用Minimax语音服务！"
                self?.voiceService.generateSpeech(text: text, voiceId: firstVoice.voiceId) { result in
                    switch result {
                    case .success(let audioData):
                        print("语音生成成功，准备播放...")
                        self?.playAudio(data: audioData)
                        
                        // 3. 同时保存到本地文件
                        self?.saveAudioToFile(data: audioData, fileName: "welcome_speech.mp3")
                        
                    case .failure(let error):
                        print("语音生成失败: \(error.localizedDescription)")
                    }
                }
                
            case .failure(let error):
                print("获取音色列表失败: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 如何在ViewController中使用
/*
class YourViewController: UIViewController {
    
    private let voiceExample = MinimaxVoiceServiceExample()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 使用示例
        voiceExample.exampleGetAllVoices()
        voiceExample.exampleGenerateSpeech()
        voiceExample.exampleCloneVoice()
        voiceExample.exampleCompleteWorkflow()
    }
}
*/
