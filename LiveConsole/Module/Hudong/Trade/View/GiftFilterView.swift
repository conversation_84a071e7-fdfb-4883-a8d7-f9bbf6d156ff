//
//  GiftFilterView.swift
//  LivePlus
//
//  Created by simon on 10.1.25.
//

import Foundation
protocol GiftFilterViewProtocol: NSObjectProtocol {
    // 选中了一个
    func didDismiss(model: GiftFilterModel?)
}
    

class GiftFilterView: UIView {
    
    weak var delegate: GiftFilterViewProtocol?
    
    public var titles: [GiftFilterModel] = GiftFilterModel.allModels
        
    private lazy var resultView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor("#59536C")
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.showsHorizontalScrollIndicator = false
        tableView.register(GiftFilterCell.self, forCellReuseIdentifier: GiftFilterCell.zl_identifier())
        tableView.cornerRadius = 6
        return tableView
    }()
  
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
   

    func makeUI() {
        self.backgroundColor = UIColor.clear
        self.addSubviews([resultView])
        resultView.snp.makeConstraints { make in
            make.width.equalTo(160)
            make.height.equalTo(260)
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(95)
        }
    }
    
   public func updateView(models:GiftFilterModel) {
        self.resultView.reloadData()
    }
    
    override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        guard isUserInteractionEnabled else { return false }
        guard !isHidden else { return false }

            
        if !self.resultView.frame.contains(point) && !self.resultView.isHidden {
            if let dele = delegate {
                dele.didDismiss(model: nil)
            }
            return false
        }
       return true
    }
}



extension GiftFilterView: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.titles.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: GiftFilterCell = tableView.dequeueReusableCell(withIdentifier: GiftFilterCell.zl_identifier(), for: indexPath) as! GiftFilterCell
        let m = self.titles[indexPath.row]
        
        cell.bind(to: m)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        self.titles.forEach({$0.selected = false})
        let m = self.titles[indexPath.row]
        m.selected = true
        self.resultView.reloadData()
        self.delegate?.didDismiss(model: m)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 40
    }
}
