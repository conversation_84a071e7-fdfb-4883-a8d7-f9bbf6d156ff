//
//  AIGiftReplyView.swift
//  LivePlus
//
//  Created by simon on 9.1.25.
//

import Foundation

enum AIGiftReplyViewType {
    case add
    case edit
    case help
}


protocol AIGiftReplyViewDelegate: NSObjectProtocol {
    func didAction(gift: DouyinGiftModel_Row?, type: AIGiftReplyViewType)
}

class AIGiftReplyView: UIView {
    
    weak var delegate: AIGiftReplyViewDelegate?
    
    ///封面图
    lazy var coverV: UIImageView = {
        let v = UIImageView()
        v.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(tapAction))
        v.addGestureRecognizer(tap)
        return v
    }()
    
    lazy var giftbg: UIView = {
        let v = UIView()
        v.cornerRadius = 6
        v.backgroundColor = UIColor("#352E4D")
        return v
    }()
    
    
    lazy var line: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor("#ADAAFF")
        return v
    }()
    
    lazy var titleLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = .white
        v.text = "触发礼物"
        return v
    }()
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor("#FCFCFC")
        v.textAlignment = .center
        return v
    }()
    
    lazy var priceLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = UIColor("#ACACAC")
        return v
    }()
    
    lazy var helpButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "Animation_帮助"), for: .normal)
        button.addTarget(self, action: #selector(helpAction), for: .touchUpInside)
        return button
    }()
    
    
    lazy var addButton: UIButton = {
        let button = UIButton()
        button.setImage(UIImage(named: "green_add"), for: .normal)
        button.addTarget(self, action: #selector(tapAction), for: .touchUpInside)
        return button
    }()
    
    var gift: DouyinGiftModel_Row?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        makeUI()
        business()
    }
    
    deinit {
        
        
    }
    
    convenience init() {
        self.init(frame: .zero)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func makeUI() {
        
        self.backgroundColor = UIColor.clear
        addSubviews([giftbg, line, helpButton, nameLab, priceLab, titleLab])
        
        giftbg.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(33)
            make.height.width.equalTo(104)
            make.leading.equalToSuperview().inset(16)
        }
        
        line.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(7)
            make.height.equalTo(14)
            make.width.equalTo(4)
            make.leading.equalToSuperview().inset(18)
        }
        
        titleLab.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(4)
            make.height.equalTo(20)
            make.width.equalTo(60)
            make.leading.equalToSuperview().inset(32)
        }
        
        
        
        helpButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(28)
            make.width.equalTo(28)
            make.leading.equalTo(titleLab.snp.trailing)
        }
        
        
        
        giftbg.addSubview(coverV)
        
        giftbg.addSubview(addButton)
        
        coverV.snp.makeConstraints { make in
            make.height.width.equalTo(76)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        addButton.snp.makeConstraints { make in
            make.height.width.equalTo(32)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }

        nameLab.snp.makeConstraints { make in
            make.top.equalTo(coverV.snp.top).offset(31)
            make.height.equalTo(20)
            make.leading.equalTo(coverV.snp.trailing).offset(20)
        }
        

        priceLab.snp.makeConstraints { make in
            make.top.equalTo(coverV.snp.top).offset(56)
            make.height.equalTo(17)
            make.leading.equalTo(coverV.snp.trailing).offset(20)
        }
    }
    
    @objc func tapAction() {
        self.delegate?.didAction(gift: nil, type: .add)
    }
    
    @objc func helpAction() {
        self.delegate?.didAction(gift: nil, type: .help)
    }
   
    
    func business() {
        
    }
    
    public func config(model: DouyinGiftModel_Row?) {

        if let image = model?.image, let urlstr = image.image.urlList.last, let url = URL(string: urlstr) {
            coverV.setImageWith(url, placeholder: UIImage(named: "占位图_首页_无直播间"))
            self.nameLab.text = image.name
            self.priceLab.text = image.diamondCount + "钻"
            self.addButton.isHidden = true
        } else {
            self.addButton.isHidden = false
            coverV.image = nil
        }
       
    }
    
}

