//
//  DouyinGiftCell.swift
//  LivePlus
//
//  Created by simon on 9.1.25.
//

import Foundation

/// 区域内容调节cell
public class DouyinGiftCell: UICollectionViewCell {
    
    ///封面图
    lazy var coverV: UIImageView = {
        let v = UIImageView()
        return v
    }()
    
    
    
    lazy var nameLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        v.textColor = UIColor("#FCFCFC")
        v.textAlignment = .center
        return v
    }()
    
    lazy var priceLab: UILabel = {
        let v = UILabel()
        v.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        v.textColor = UIColor("#ACACAC")
        v.textAlignment = .center
        return v
    }()
    
        
    /// cell的数据模型
    public var _model: DouyinGiftModel_Row?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.backgroundColor = UIColor("#352E4D")
        
        contentView.addSubview(coverV)
        coverV.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.width.equalTo(58)
            make.centerX.equalToSuperview()
        }
        
        contentView.addSubview(nameLab)
        nameLab.snp.makeConstraints { make in
            make.top.equalTo(coverV.snp.bottom).offset(7)
            make.height.equalTo(22)
            make.leading.trailing.equalToSuperview()
        }
        
        contentView.addSubview(priceLab)
        priceLab.snp.makeConstraints { make in
            make.top.equalTo(nameLab.snp.bottom)
            make.height.equalTo(17)
            make.leading.trailing.equalToSuperview()
        }
        
    }
    
   
    /// cell配置
    public func config(model: DouyinGiftModel_Row) {
        self._model = model
        if let image = model.image, let urlstr = image.image.urlList.last, let url = URL(string: urlstr) {
            coverV.setImageWith(url, placeholder: UIImage(named: "占位图_首页_无直播间"))
            self.nameLab.text = image.name
            self.priceLab.text = image.diamondCount + "钻"
            
            if model.enable == false {
                coverV.alpha = 0.5
            } else {
                coverV.alpha = 1
            }
        }
       
       
    }
    
   
}
